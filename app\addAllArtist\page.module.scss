@import '../global';

$mainImageSize: 450px;

.mainContainer {
    @include mainPageStyles;
    width: max-content;

    // Top Buttons
    .topButtonsContainer {
        display: flex;
        gap: 18px;
        align-items: center;
        margin-top: 5px;
    }

    // Process
    .processContainer {
        @include processContainerStyles;
        @include hideScrollbar;
        position: relative;
        width: 400px;
        height: 583px;
        max-height: calc(100vh - 350px);
        color: $offWhite;
        overflow-y: scroll;

        // Waiting State
        &.waiting {
            background-color: $veryDarkPrimaryColor;
        }

        // Ready
        .readyText {
            position: absolute;
            top: 50%; left: 50%; transform: translate(-50%, -50%);
            color: $offWhite;
            text-align: center;
            font-size: 44px;
            user-select: none;
        }

        // Group Heading
        .itemGroupHeading {
            float: left;
            margin: 18px 0;
            grid-column: 1 / 3;
            width: max-content;
            padding: 5px 10px;
            border-radius: $smallBorderRadius;
            background-color: $primaryColor;
            font-size: 16px;
        }

        // Progress Items
        .progressItem {
            margin: 10px 0;
            width: 100%;
            height: max-content;
            display: flex;
            align-items: center;
            column-gap: 20px;

            &:hover {
                background-color: $veryDarkPrimaryColor;
            }

            // Image
            .itemImage {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: $smallImageBorderRadius;
            }

            // Title
            .progressTitle {
                display: flex;
                font-size: 20px;
                color: $offWhite;
            }
        }
    }

    // Fields Container
    .fieldsContainer {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        row-gap: 10px;

        // Field with Spinner Container
        .fieldWithSpinner {
            position: relative;
            width: 100%;
        }

        // Field Spinner
        .fieldSpinner {
            width: max-content;
            height: max-content;
            position: absolute;
            top: 43px;
            right: -36px;
            z-index: 10;
            > img { top: 0; left: 0; }
        }

        // Artist URL
        .inputOverride {
            width: 530px !important;
            &>input:not(:focus) {
                cursor: pointer !important;
            }
        }

        // Playlist Tile Container
        .playlistTileContainer {
            display: flex;
            justify-content: center;
            margin: 10px 0;

            .playlistTile {
                width: 120px;
                height: max-content;
                display: flex;
                flex-direction: column;
                align-items: center;
                border-radius: $smallImageBorderRadius;
                cursor: pointer;

                &:hover {
                    background-color: $veryDarkPrimaryColor;
                }

                .playlistTileImage {
                    width: 100%;
                    height: auto;
                    object-fit: cover;
                    border-radius: $smallImageBorderRadius;
                    margin-bottom: 5px;
                }

                .playlistTileTitle {
                    width: 100%;
                    text-align: center;
                    padding: 2px 4px 6px 4px;
                    font-size: 12px;
                    color: $offWhite;
                    line-height: 1.2;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .playlistTilePlaceholder {
                width: 120px;
                height: max-content;
                display: flex;
                flex-direction: column;
                align-items: center;

                .playlistTilePlaceholderImage {
                    width: 120px;
                    height: 120px;
                    background-color: $veryDarkPrimaryColor;
                    border-radius: $smallImageBorderRadius;
                    margin-bottom: 5px;
                }

                .playlistTilePlaceholderTitle {
                    width: 120px;
                    height: 20px;
                    background-color: $veryDarkPrimaryColor;
                    border-radius: $tinyBorderRadius;
                    margin-top: 2px;
                }
            }
        }
    }

    // Artist Stats
    .artistStatsContainer {
        margin-top: 5px;
        margin-bottom: 45px;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 12px;
        border-radius: $tinyBorderRadius;
        cursor: pointer;

        &:hover {
            background-color: $veryDarkPrimaryColor;
        }

        // When no artist data, remove hover and cursor
        &:not([data-clickable="true"]) {
            cursor: default;

            &:hover {
                background-color: transparent;
            }
        }
    }

    .artistStatsRow {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        width: 100%;
        max-width: 400px;
        height: 55px;
        padding: 10px 0 0 15px;
        box-sizing: border-box;

        // Only show background when no data (placeholder state)
        &.placeholder {
            background-color: $veryDarkPrimaryColor;
            border-radius: $tinyBorderRadius;
        }

        .artistStatsGroup {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            flex: 1;

            &:first-child {
                flex: unset;
                width: 180px;
                text-align: left;
                align-items: flex-start;

                .artistStatsValue {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 100%;
                    font-size: 18px;
                    font-weight: bold;
                }
            }

            .artistStatsLabel {
                color: $primaryColor;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 5px;
            }

            .artistStatsValue {
                color: $offWhite;
                font-size: 16px;
                font-weight: bold;
            }
        }
    }

    .artistStatsImage {
        width: 395px;
        height: 165px;
        object-fit: cover;
        border-radius: $tinyBorderRadius;
    }

    .artistStatsImagePlaceholder {
        width: 395px;
        height: 165px;
        background-color: $veryDarkPrimaryColor;
        border-radius: $tinyBorderRadius;
    }

    .modalContainer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        row-gap: 20px;
    }
}