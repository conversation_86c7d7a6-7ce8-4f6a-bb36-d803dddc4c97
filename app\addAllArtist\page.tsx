"use client"

import styles from "./page.module.scss";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSession } from "next-auth/react";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Field from "../components/JC_Field/JC_Field";
import <PERSON><PERSON>_<PERSON><PERSON> from "../components/JC_Button/JC_Button";
import JC_ModalConfirmation from "../components/JC_ModalConfirmation/JC_ModalConfirmation";
import { JC_Get } from "../apiServices/JC_Get";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { YtAddItem } from "../apiServices/ytAddItem";
import { JC_Utils } from "../Utils";
import { D_UserConfig, UserConfigModel } from "../models/UserConfig";
import { PlaylistModel } from "../models/Playlist";
import { UserProcessProgressModel, ProgressData_AddAllArtist, D_UserProcessProgress } from "../models/UserProcessProgress";
import { ArtistFullModel } from "../models/Artist";
import { ArtistItemModel } from "../models/ArtistItem";
import { JC_ConfirmationModalUsageModel } from "../models/ComponentModels/JC_ConfirmationModalUsage";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { UserProcessProgressCodeEnum } from "../enums/UserProcessProgressCode";
import { YtMusicItemTypeEnum } from "../enums/YtMusicItemType";
import JC_Modal from "../components/JC_Modal/JC_Modal";


export default function Page_AddAllArtist() {

    const session = useSession();
    const userId = session.data?.user.Id ?? JC_Utils.getLocalUserId();

    // - STATE - //

    // Initialised
    const [isInitialised, setIsInitialised] = useState<boolean>(false);
    // Loading
    const [processHasRan, setProcessHasRan] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isArtistUrlLoading, setIsArtistUrlLoading] = useState<boolean>(false);
    const [isPlaylistUrlLoading, setIsPlaylistUrlLoading] = useState<boolean>(false);
    // Artist
    const [artistUrl, setArtistUrl] = useState<string>();
    const [artistData, setArtistData] = useState<ArtistFullModel|null>();
    const [artistUrlError, setArtistUrlError] = useState<string>("");
    // User Config
    const [userConfig, setUserConfig] = useState<UserConfigModel>();
    const [yourPlaylistData, setYourPlaylistData] = useState<PlaylistModel|null>();
    const [yourPlaylistName, setYourPlaylistName] = useState<string|null>();
    const [yourPlaylistError, setYourPlaylistError] = useState<string>("");
    // Progress
    const [progressItems, setProgressItems] = useState<ArtistItemModel[]>([]);
    // Confirmation
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>();
    // Modal
    const [modalText, setModalText] = useState<string>("");


    // - INITIALISE - //

    useEffect(() => {
        JC_Get<UserConfigModel>(UserConfigModel, "userConfig", { userId: userId }).then(async userConfig => {
            yourPlaylistUrlChanged(userConfig ?? D_UserConfig(userId));
            setTimeout(() => document.getElementById(`artistUrl`)?.focus(), 10);

            // - TESTING - //
            // artistUrlChanged("https://music.youtube.com/channel/UCeXSgJXSU84EQaqG5OA5CMw"); // DAMO xo
            // artistUrlChanged("https://music.youtube.com/channel/UCWR_-sd1qBK7HtJTgw0Djkw"); // Indila
            // artistUrlChanged("https://music.youtube.com/channel/UCwUnKtdngzXuE4hCAHYFQkA"); // Nectry
            // artistUrlChanged("https://music.youtube.com/channel/UCm9xJt17Wv_UVr_9K9vs8gg"); // Kevin Sherwood
            // yourPlaylistUrlChanged("https://music.youtube.com/browse/VLPLMc_DKXs_N0OkWkgmkGUqla0MfrmpnUmG");
            // ----------- //

            // Resume current process if still running
            await checkExistingProcess();
            setIsInitialised(true);
        });
    }, []);


    // - HANDLES - //

    // Artist URL change
    async function artistUrlChanged(newUrl:string) {
        setArtistUrl(newUrl);
        setArtistData(null);
        setArtistUrlError("");
        setProcessHasRan(false);
        if (!JC_Utils.stringNullOrEmpty(newUrl)) {
            setIsArtistUrlLoading(true);
            let artist:ArtistFullModel|null = null;
            try {
                artist = await JC_Get(ArtistFullModel, "ytGetArtist", {
                    artistUrl: newUrl
                });
            } catch (e) {}
            if (artist != null) {
                setArtistData(artist);
            } else {
                setArtistUrlError("Artist URL is invalid!");
            }
            setIsArtistUrlLoading(false);
        }
    }

    // Your playlist URL change
    async function yourPlaylistUrlChanged(newUserConfig:UserConfigModel) {
        setUserConfig(newUserConfig);
        setYourPlaylistData(null);
        setYourPlaylistName(null);
        setYourPlaylistError("");
        setProcessHasRan(false);
        if (!JC_Utils.stringNullOrEmpty(newUserConfig.NominatedToListenPlaylistUrl)) {
            setIsPlaylistUrlLoading(true);
            let playlist:PlaylistModel|null = null;
            try {
                playlist = await JC_Get(PlaylistModel, "playlist", {
                    playlistUrl: newUserConfig.NominatedToListenPlaylistUrl
                });
            } catch (e) {}
            if (playlist != null) {
                setYourPlaylistData(playlist);
                setYourPlaylistName(playlist?.Title);
            } else {
                setYourPlaylistError("Playlist URL is invalid!");
            }
            setIsPlaylistUrlLoading(false);
        }
    }

    // Clear Playlist
    async function clearPlaylistAttempt() {
        if (session.data == null) {
            setModalText("This function is only available if you are signed in.");
        } else {
            setConfirmationModalData({
                width: "380px",
                title: "Clear Playlist",
                text: `Are you sure you would like to remove all items from your "${yourPlaylistName}" playlist? (Only 100 songs can be removed at a time)`,
                submitButtons: [{
                    text: "Clear",
                    onSubmit: () => {
                        // Get YouTube Music credentials
                        const authorization = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicAuth) || '';
                        const cookie = localStorage.getItem(LocalStorageKeyEnum.JC_YtMusicCookie) || '';
                        JC_PostRaw("ytRemoveAllFromPlaylist", {
                            playlistUrl: userConfig!.NominatedToListenPlaylistUrl,
                            ytMusicAuth: authorization,
                            ytMusicCookie: cookie
                        });
                        setConfirmationModalData(null);
                        JC_Utils.showToastSuccess("Playlist has been cleared.");
                    }
                }]
            });
        }
    }

    // Check and continue existing process that may be half-done
    async function checkExistingProcess() {
        let dbProgress = await JC_Get<UserProcessProgressModel>(UserProcessProgressModel, "userProcessProgress", { userId: userId, processCode: UserProcessProgressCodeEnum.AddAllArtist });
        if (dbProgress?.Data != null && dbProgress.ItemsLeftJson != null) {
            const itemsLeft: ArtistItemModel[] = JSON.parse(dbProgress.ItemsLeftJson);
            if (itemsLeft.length > 0) {
                await artistUrlChanged(dbProgress.Data.ArtistUrl);
                process(dbProgress, false);
            }
        }
    }

    // Run
    async function process(existingProgress?:UserProcessProgressModel, updateUserConfig:boolean = true) {

        setIsLoading(true);
        setProcessHasRan(true);
        setProgressItems([]);

        // Update nominated playlist in UserConfig
        if (updateUserConfig && userConfig) {
            JC_Post(UserConfigModel, "userConfig", userConfig);
        }

        // Get progress
        let dbProgress:UserProcessProgressModel = existingProgress ?? await JC_Get<UserProcessProgressModel>(UserProcessProgressModel, "userProcessProgress", { userId: userId, processCode: UserProcessProgressCodeEnum.AddAllArtist });

        let itemsCompleted: ArtistItemModel[] = [];
        let itemsLeft: ArtistItemModel[] = [];

        if (dbProgress?.Data == null || dbProgress.ItemsLeftJson == null || JSON.parse(dbProgress.ItemsLeftJson).length == 0) {
            let notCreatedYet = dbProgress?.Data == null;
            let artist:ArtistFullModel = await JC_Get<ArtistFullModel>(ArtistFullModel, "ytGetArtist", {
                artistUrl: artistUrl
            });
            if (session.data == null) {
                artist.Singles = artist.Singles.slice(0, 3);
                artist.Albums = [];
            }

            // Replace ImageUrls with cached blob URLs for items that will be rendered
            const allItems = [...artist.Singles, ...artist.Albums];
            const cachedItems = await JC_PostRaw("imageCache", allItems);

            let progressData:ProgressData_AddAllArtist = {
                ArtistUrl: artistUrl!,
                YourPlaylistUrl: userConfig!.NominatedToListenPlaylistUrl!
            };

            itemsCompleted = [];
            itemsLeft = cachedItems;

            dbProgress = {
                ...(notCreatedYet ? D_UserProcessProgress(userId) : dbProgress),
                ProcessCode: UserProcessProgressCodeEnum.AddAllArtist,
                ProgressDataJson: JSON.stringify(progressData),
                ItemsCompletedJson: JSON.stringify(itemsCompleted),
                ItemsLeftJson: JSON.stringify(itemsLeft),
                Data: progressData
            };
            setProgressItems(itemsCompleted);
            await JC_Post(UserProcessProgressModel, "userProcessProgress", dbProgress);
        } else {
            // Deserialize existing progress
            itemsCompleted = dbProgress.ItemsCompletedJson ? JSON.parse(dbProgress.ItemsCompletedJson) : [];
            itemsLeft = dbProgress.ItemsLeftJson ? JSON.parse(dbProgress.ItemsLeftJson) : [];
            setProgressItems(itemsCompleted);
        }

        // Process
        while (itemsLeft.length > 0) {
            let item = itemsLeft.shift()! as ArtistItemModel;
            itemsCompleted.push(item);
            setProgressItems([...itemsCompleted]);
            setTimeout(() => document.getElementById("processContainer") != null ? document.getElementById("processContainer")!.scrollTop = (document.getElementById("processContainer")?.scrollHeight??1000)+200 : null, 50);
            // Wait a bit before adding next item to give time for YT to finish adding last items
            await JC_Utils.sleep(0.5);
            // Add item
            await YtAddItem(dbProgress.Data!.YourPlaylistUrl, item.Id);
            // Update progress using the new AdjustItems function
            await UserProcessProgressModel.AdjustItems(userId, UserProcessProgressCodeEnum.AddAllArtist, [item]);
        }

        // Finished
        setIsLoading(false);
        setTimeout(() => document.getElementById("processContainer") != null ? document.getElementById("processContainer")!.scrollTop = (document.getElementById("processContainer")?.scrollHeight??1000)+200 : null, 50);
    }


    // - MAIN - //

    return !isInitialised ? <JC_Spinner /> : <div className={styles.mainContainer}>

        {/* Title */}
        <JC_Title title="Add All of Artist" />

        {/* Top Buttons */}
        <div className={styles.topButtonsContainer}>
            {/* Run */}
            <JC_Button
                overrideClass={styles.runButtonOverride}
                text="Run"
                isSecondary
                onClick={() => process()}
                isDisabled={JC_Utils.stringNullOrEmpty(artistData?.Name) || JC_Utils.stringNullOrEmpty(yourPlaylistName) || isLoading || isArtistUrlLoading || isPlaylistUrlLoading}
            />
            {/* Clear Playlist */}
            <JC_Button
                overrideClass={styles.clearPlaylistButton}
                text="Clear Playlist"
                isSecondary
                onClick={clearPlaylistAttempt}
                isDisabled={JC_Utils.stringNullOrEmpty(yourPlaylistName) || isLoading || isArtistUrlLoading || isPlaylistUrlLoading}
            />
        </div>

        {/* Process */}
        <div id="processContainer" className={`${styles.processContainer} ${!processHasRan && (JC_Utils.stringNullOrEmpty(artistData?.Name) || JC_Utils.stringNullOrEmpty(yourPlaylistName) || isLoading || isArtistUrlLoading || isPlaylistUrlLoading) ? styles.waiting : ""}`}>
            {!processHasRan && !(JC_Utils.stringNullOrEmpty(artistData?.Name) || JC_Utils.stringNullOrEmpty(yourPlaylistName) || isLoading || isArtistUrlLoading || isPlaylistUrlLoading) && <div className={styles.readyText}>READY</div>}
            {processHasRan &&
            <React.Fragment>
                <div style={{ fontSize: "20px" }}>Adding...</div>
                {progressItems.map(item =>
                <React.Fragment key={item.Id}>
                    {item.Id == progressItems.find(i => i.Type == YtMusicItemTypeEnum.Single)?.Id && <div className={styles.itemGroupHeading}>SINGLES</div>}
                    {item.Id == progressItems.find(i => i.Type == YtMusicItemTypeEnum.Album)?.Id  && <div className={styles.itemGroupHeading}>ALBUMS</div>}
                    <Link href={`https://music.youtube.com/playlist?list=${item.PlaylistId}`} target="_blank" className={styles.progressItem}>
                        <Image
                            className={styles.itemImage}
                            src={item.ImageUrl}
                            width={100}
                            height={100}
                            alt="AlbumCover"
                            unoptimized
                        />
                        <div className={styles.progressTitle}>{item.Title}</div>
                    </Link>
                </React.Fragment>)}
                {!isLoading && <div style={{ marginTop: "20px", fontSize: "20px" }}>Finished!</div>}
            </React.Fragment>}
        </div>

        {/* Form Fields */}
        <div className={styles.fieldsContainer}>
            {/* Artist URL */}
            <div className={styles.fieldWithSpinner}>
                <JC_Field
                    inputOverrideClass={styles.inputOverride}
                    inputId="artistUrl"
                    type={FieldTypeEnum.Text}
                    label="Artist Url"
                    readOnly={isLoading || isArtistUrlLoading}
                    value={artistUrl}
                    onFocus={() => (document.getElementById("artistUrl") as HTMLInputElement).select()}
                    onChange={(newValue: string) => artistUrlChanged(newValue)}
                />
                {isArtistUrlLoading && <JC_Spinner overrideClass={styles.fieldSpinner} isSmall />}
            </div>

            {/* Artist Stats */}
            <div
                className={styles.artistStatsContainer}
                data-clickable={artistData ? "true" : "false"}
                onClick={artistData ? () => window.open(artistUrl!, '_blank') : undefined}
            >
                <div className={`${styles.artistStatsRow} ${!artistData ? styles.placeholder : ''}`}>
                    <div className={styles.artistStatsGroup}>
                        <div className={styles.artistStatsValue}>
                            {artistData?.Name}
                        </div>
                    </div>
                    {artistData && (
                        <>
                            <div className={styles.artistStatsGroup}>
                                <div className={styles.artistStatsLabel}>Singles</div>
                                <div className={styles.artistStatsValue}>{artistData.Singles?.length || 0}</div>
                            </div>
                            <div className={styles.artistStatsGroup}>
                                <div className={styles.artistStatsLabel}>Albums</div>
                                <div className={styles.artistStatsValue}>{artistData.Albums?.length || 0}</div>
                            </div>
                        </>
                    )}
                </div>
                {artistData ? (
                    <Image
                        className={styles.artistStatsImage}
                        src={artistData.ImageUrl}
                        width={395}
                        height={165}
                        alt="Artist"
                        unoptimized
                    />
                ) : (
                    <div className={styles.artistStatsImagePlaceholder}></div>
                )}
            </div>

            {/* Your Playlist URL */}
            <div className={styles.fieldWithSpinner}>
                <JC_Field
                    inputOverrideClass={styles.inputOverride}
                    inputId="artistSongsPlaylisitUrl"
                    type={FieldTypeEnum.Text}
                    label="Your Playlist To Add Songs To"
                    readOnly={isLoading || isPlaylistUrlLoading}
                    value={userConfig?.NominatedToListenPlaylistUrl ?? ""}
                    onFocus={() => (document.getElementById("artistSongsPlaylisitUrl") as HTMLInputElement).select()}
                    onChange={(newValue: string) => yourPlaylistUrlChanged({ ...userConfig!, NominatedToListenPlaylistUrl: newValue })}
                />
                {isPlaylistUrlLoading && <JC_Spinner overrideClass={styles.fieldSpinner} isSmall />}
            </div>

            {/* To Playlist Display */}
            {yourPlaylistData ? (
                <div className={styles.playlistTileContainer}>
                    <div
                        className={styles.playlistTile}
                        onClick={() => window.open(userConfig?.NominatedToListenPlaylistUrl!, '_blank')}
                    >
                        <Image
                            className={styles.playlistTileImage}
                            src={yourPlaylistData.ImageUrl || '/default-playlist.png'}
                            width={70}
                            height={70}
                            alt="PlaylistCover"
                            unoptimized
                        />
                        <div className={styles.playlistTileTitle}>{yourPlaylistData.Title}</div>
                    </div>
                </div>
            ) : (
                <div className={styles.playlistTileContainer}>
                    <div className={styles.playlistTilePlaceholder}>
                        <div className={styles.playlistTilePlaceholderImage}></div>
                        <div className={styles.playlistTilePlaceholderTitle}></div>
                    </div>
                </div>
            )}
        </div>



        {/* Confirmation */}
        {confirmationModalData &&
        <JC_ModalConfirmation
            width={confirmationModalData.width}
            title={confirmationModalData.title}
            text={confirmationModalData.text}
            isOpen={confirmationModalData != null}
            onCancel={() => setConfirmationModalData(null)}
            submitButtons={confirmationModalData.submitButtons}
        />}

        {/* Error Modal */}
        <JC_Modal overrideClass={styles.modalContainer} isOpen={!JC_Utils.stringNullOrEmpty(modalText)} onCancel={() => setModalText("")}>
            {modalText}
            <JC_Button text="Ok" onClick={() => setModalText("")} />
        </JC_Modal>

    </div>;
}
